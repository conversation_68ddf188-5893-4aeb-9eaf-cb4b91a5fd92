# 设备点位互锁功能说明

## 功能概述

设备点位互锁功能提供了一个图形化的界面来配置设备之间的互锁关系。当某个设备的点位值满足特定条件时，系统会自动锁定另一个设备的指定点位，防止误操作或确保安全运行。

## 主要特性

### 1. 分层图形化配置界面

#### 第一层：设备和点位选择
- **左侧区域**：触发设备选择
  - 选择触发设备
  - 选择触发点位（根据设备自动加载）
- **中间区域**：直观的箭头指示互锁关系
- **右侧区域**：被锁设备选择
  - 选择被锁设备
  - 选择被锁点位（根据设备自动加载）

#### 第二层：触发条件设置
- 选择触发条件符号（等于、不等于、大于、小于等）
- 设置触发值

#### 第三层：提示信息和备注
- 设置互锁触发时的提示信息
- 添加内部管理备注

### 2. 智能联动选择
- 选择设备后，自动加载该设备下的所有点位
- 支持搜索和过滤功能
- 防止无效配置

### 3. 增强的查询功能
- 支持按设备、点位筛选
- 下拉选择替代手动输入ID
- 提高查询准确性

### 4. 优化的显示效果
- 表格显示设备名称和点位名称而非ID
- 符号用不同颜色的标签显示
- 提示和备注支持溢出显示

## 使用流程

### 新增互锁配置

1. **点击"新增"按钮**
   - 打开分层图形化配置对话框

2. **第一步：选择设备和点位**
   - 左侧选择触发设备和触发点位
   - 右侧选择被锁设备和被锁点位
   - 点位选项根据选择的设备自动加载

3. **第二步：设置触发条件**
   - 选择触发条件符号（==、!=、>、<、>=、<=）
   - 输入触发值

4. **第三步：完善信息**
   - 输入互锁触发时的提示信息
   - 添加备注信息（可选）

5. **确认保存**
   - 系统会验证所有表单的完整性
   - 保存成功后返回列表页面

### 修改互锁配置

1. **点击列表中的"修改"按钮**
2. **系统自动加载现有配置**
   - 根据已选设备自动加载对应点位选项
3. **修改相关配置**
4. **保存更改**

### 查询互锁配置

1. **使用顶部筛选条件**
   - 按设备筛选
   - 按点位筛选
   - 按被锁设备筛选
   - 按被锁点位筛选

2. **查看列表结果**
   - 显示设备名称和点位名称
   - 符号用彩色标签显示
   - 支持批量操作

## 技术实现

### 前端组件
- 使用 Element UI 的 Select、Card、Row/Col 等组件
- 响应式布局，支持移动端显示
- 表单验证确保数据完整性

### API 接口
- `deviceOption()` - 获取设备选项列表
- `devicePointOption()` - 获取点位选项列表
- 支持按设备ID筛选点位

### 数据流转
1. 页面加载时获取所有设备和点位选项
2. 设备选择变化时动态加载对应点位
3. 表单提交时进行双重验证
4. 数据保存后刷新列表显示

## 注意事项

1. **设备选择顺序**：必须先选择设备，才能选择对应的点位
2. **触发条件设置**：确保触发值的数据类型与点位数据类型匹配
3. **互锁逻辑**：避免创建循环互锁，可能导致系统死锁
4. **权限控制**：根据用户权限显示相应的操作按钮

## 后续优化建议

1. **可视化拓扑图**：显示设备间的互锁关系图
2. **批量配置**：支持批量创建相似的互锁规则
3. **模板功能**：保存常用的互锁配置模板
4. **实时状态**：显示互锁规则的当前状态和触发历史

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备id" prop="deviceId">
        <el-input
          v-model="queryParams.deviceId"
          placeholder="请输入设备id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="点位id" prop="pointId">
        <el-input
          v-model="queryParams.pointId"
          placeholder="请输入点位id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被锁设备id" prop="lockDeviceId">
        <el-input
          v-model="queryParams.lockDeviceId"
          placeholder="请输入被锁设备id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被锁点位id" prop="lockPointId">
        <el-input
          v-model="queryParams.lockPointId"
          placeholder="请输入被锁点位id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:pointLock:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:pointLock:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:pointLock:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['biz:pointLock:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pointLockList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备id" align="center" prop="deviceId" />
      <el-table-column label="点位id" align="center" prop="pointId" />
      <el-table-column label="被锁设备id" align="center" prop="lockDeviceId" />
      <el-table-column label="被锁点位id" align="center" prop="lockPointId" />
      <el-table-column label="互锁值" align="center" prop="lockValue" />
      <el-table-column label="符号" align="center" prop="symbol" />
      <el-table-column label="提示" align="center" prop="hint" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:pointLock:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:pointLock:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改点位锁对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备id" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入设备id" />
        </el-form-item>
        <el-form-item label="点位id" prop="pointId">
          <el-input v-model="form.pointId" placeholder="请输入点位id" />
        </el-form-item>
        <el-form-item label="被锁设备id" prop="lockDeviceId">
          <el-input v-model="form.lockDeviceId" placeholder="请输入被锁设备id" />
        </el-form-item>
        <el-form-item label="被锁点位id" prop="lockPointId">
          <el-input v-model="form.lockPointId" placeholder="请输入被锁点位id" />
        </el-form-item>
        <el-form-item label="互锁值" prop="lockValue">
          <el-input v-model="form.lockValue" placeholder="请输入互锁值" />
        </el-form-item>
        <el-form-item label="符号" prop="symbol">
          <el-input v-model="form.symbol" placeholder="请输入符号" />
        </el-form-item>
        <el-form-item label="提示" prop="hint">
          <el-input v-model="form.hint" placeholder="请输入提示" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPointLock, getPointLock, delPointLock, addPointLock, updatePointLock } from "@/api/biz/pointLock";

export default {
  name: "PointLock",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 点位锁表格数据
      pointLockList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: undefined,
        pointId: undefined,
        lockDeviceId: undefined,
        lockPointId: undefined,
        lockValue: undefined,
        symbol: undefined,
        hint: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "ID不能为空", trigger: "blur" }
        ],
        deviceId: [
          { required: true, message: "设备id不能为空", trigger: "blur" }
        ],
        pointId: [
          { required: true, message: "点位id不能为空", trigger: "blur" }
        ],
        lockDeviceId: [
          { required: true, message: "被锁设备id不能为空", trigger: "blur" }
        ],
        lockPointId: [
          { required: true, message: "被锁点位id不能为空", trigger: "blur" }
        ],
        lockValue: [
          { required: true, message: "互锁值不能为空", trigger: "blur" }
        ],
        symbol: [
          { required: true, message: "符号不能为空", trigger: "blur" }
        ],
        hint: [
          { required: true, message: "提示不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询点位锁列表 */
    getList() {
      this.loading = true;
      listPointLock(this.queryParams).then(response => {
        this.pointLockList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deviceId: undefined,
        pointId: undefined,
        lockDeviceId: undefined,
        lockPointId: undefined,
        lockValue: undefined,
        symbol: undefined,
        hint: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加点位锁";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getPointLock(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改点位锁";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updatePointLock(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addPointLock(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除点位锁编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delPointLock(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/pointLock/export', {
        ...this.queryParams
      }, `pointLock_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

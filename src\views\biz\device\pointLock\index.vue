<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备" prop="deviceId">
        <el-select
          v-model="queryParams.deviceId"
          placeholder="请选择设备"
          clearable
          filterable
          @keyup.enter.native="handleQuery"
          @change="handleQueryDeviceChange"
        >
          <el-option
            v-for="device in deviceOptions"
            :key="device.value"
            :label="device.label"
            :value="device.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="点位" prop="pointId">
        <el-select
          v-model="queryParams.pointId"
          placeholder="请选择点位"
          clearable
          filterable
          @keyup.enter.native="handleQuery"
          :disabled="!queryParams.deviceId"
        >
          <el-option
            v-for="point in queryPointOptions"
            :key="point.value"
            :label="point.label"
            :value="point.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="被锁设备" prop="lockDeviceId">
        <el-select
          v-model="queryParams.lockDeviceId"
          placeholder="请选择被锁设备"
          clearable
          filterable
          @keyup.enter.native="handleQuery"
          @change="handleQueryLockDeviceChange"
        >
          <el-option
            v-for="device in deviceOptions"
            :key="device.value"
            :label="device.label"
            :value="device.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="被锁点位" prop="lockPointId">
        <el-select
          v-model="queryParams.lockPointId"
          placeholder="请选择被锁点位"
          clearable
          filterable
          @keyup.enter.native="handleQuery"
          :disabled="!queryParams.lockDeviceId"
        >
          <el-option
            v-for="point in queryLockPointOptions"
            :key="point.value"
            :label="point.label"
            :value="point.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:pointLock:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:pointLock:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:pointLock:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['biz:pointLock:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pointLockList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备" align="center" prop="deviceName" />
      <el-table-column label="点位" align="center" prop="pointName" />
      <el-table-column label="被锁设备" align="center" prop="lockDeviceName" />
      <el-table-column label="被锁点位" align="center" prop="lockPointName" />
      <el-table-column label="互锁值" align="center" prop="lockValue" />
      <el-table-column label="符号" align="center" prop="symbol">
        <template slot-scope="scope">
          <el-tag :type="getSymbolType(scope.row.symbol)">{{ scope.row.symbol }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="提示" align="center" prop="hint" show-overflow-tooltip />
      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:pointLock:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:pointLock:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改点位锁对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <!-- 第一行：设备和点位选择 -->
      <el-row :gutter="20" class="device-selection-row">
        <!-- 左侧：触发设备选择 -->
        <el-col :span="11">
          <div class="section-title">
            <i class="el-icon-setting"></i>
            触发设备选择
          </div>
          <el-card shadow="never" class="selection-card">
            <el-form ref="triggerForm" :model="form" :rules="triggerRules" label-width="80px">
              <el-form-item label="触发设备" prop="deviceId">
                <el-select
                  v-model="form.deviceId"
                  placeholder="请选择触发设备"
                  filterable
                  clearable
                  style="width: 100%"
                  @change="handleTriggerDeviceChange"
                >
                  <el-option
                    v-for="device in deviceOptions"
                    :key="device.value"
                    :label="device.label"
                    :value="device.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="触发点位" prop="pointId">
                <el-select
                  v-model="form.pointId"
                  placeholder="请选择触发点位"
                  filterable
                  clearable
                  style="width: 100%"
                  :disabled="!form.deviceId"
                >
                  <el-option
                    v-for="point in triggerPointOptions"
                    :key="point.value"
                    :label="point.label"
                    :value="point.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 中间：箭头指示 -->
        <el-col :span="2" class="arrow-col">
          <div class="arrow-container">
            <i class="el-icon-right arrow-icon"></i>
            <div class="arrow-text">互锁</div>
          </div>
        </el-col>

        <!-- 右侧：被锁设备选择 -->
        <el-col :span="11">
          <div class="section-title">
            <i class="el-icon-lock"></i>
            被锁设备选择
          </div>
          <el-card shadow="never" class="selection-card">
            <el-form ref="lockForm" :model="form" :rules="lockRules" label-width="80px">
              <el-form-item label="被锁设备" prop="lockDeviceId">
                <el-select
                  v-model="form.lockDeviceId"
                  placeholder="请选择被锁设备"
                  filterable
                  clearable
                  style="width: 100%"
                  @change="handleLockDeviceChange"
                >
                  <el-option
                    v-for="device in deviceOptions"
                    :key="device.value"
                    :label="device.label"
                    :value="device.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="被锁点位" prop="lockPointId">
                <el-select
                  v-model="form.lockPointId"
                  placeholder="请选择被锁点位"
                  filterable
                  clearable
                  style="width: 100%"
                  :disabled="!form.lockDeviceId"
                >
                  <el-option
                    v-for="point in lockPointOptions"
                    :key="point.value"
                    :label="point.label"
                    :value="point.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第二行：触发条件设置 -->
      <el-row :gutter="20" class="condition-row">
        <el-col :span="24">
          <div class="section-title">
            <i class="el-icon-s-operation"></i>
            触发条件设置
          </div>
          <el-card shadow="never" class="condition-card">
            <el-form ref="conditionForm" :model="form" :rules="conditionRules" label-width="80px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="触发条件" prop="symbol">
                    <el-select v-model="form.symbol" placeholder="请选择条件符号" style="width: 100%">
                      <el-option label="等于 (==)" value="==" />
                      <el-option label="不等于 (!=)" value="!=" />
                      <el-option label="大于 (>)" value=">" />
                      <el-option label="小于 (<)" value="<" />
                      <el-option label="大于等于 (>=)" value=">=" />
                      <el-option label="小于等于 (<=)" value="<=" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="触发值" prop="lockValue">
                    <el-input v-model="form.lockValue" placeholder="请输入触发值" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第三行：提示信息和备注 -->
      <el-row :gutter="20" class="info-row">
        <el-col :span="24">
          <div class="section-title">
            <i class="el-icon-info"></i>
            提示信息和备注
          </div>
          <el-card shadow="never" class="info-card">
            <el-form ref="infoForm" :model="form" :rules="infoRules" label-width="80px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="提示信息" prop="hint">
                    <el-input
                      v-model="form.hint"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入互锁触发时的提示信息，用户将看到此提示"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注信息" prop="remark">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注信息，用于内部管理和说明"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPointLock, getPointLock, delPointLock, addPointLock, updatePointLock } from "@/api/biz/pointLock";
import { deviceOption } from "@/api/biz/device";
import { devicePointOption } from "@/api/biz/devicePoint";

export default {
  name: "PointLock",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 点位锁表格数据
      pointLockList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 设备选项
      deviceOptions: [],
      // 点位选项
      pointOptions: [],
      // 触发点位选项
      triggerPointOptions: [],
      // 被锁点位选项
      lockPointOptions: [],
      // 查询条件用的点位选项
      queryPointOptions: [],
      // 查询条件用的被锁点位选项
      queryLockPointOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: undefined,
        pointId: undefined,
        lockDeviceId: undefined,
        lockPointId: undefined,
        lockValue: undefined,
        symbol: undefined,
        hint: undefined,
      },
      // 表单参数
      form: {},
      // 触发设备表单校验
      triggerRules: {
        deviceId: [
          { required: true, message: "触发设备不能为空", trigger: "change" }
        ],
        pointId: [
          { required: true, message: "触发点位不能为空", trigger: "change" }
        ]
      },
      // 被锁设备表单校验
      lockRules: {
        lockDeviceId: [
          { required: true, message: "被锁设备不能为空", trigger: "change" }
        ],
        lockPointId: [
          { required: true, message: "被锁点位不能为空", trigger: "change" }
        ]
      },
      // 触发条件表单校验
      conditionRules: {
        symbol: [
          { required: true, message: "触发条件不能为空", trigger: "change" }
        ],
        lockValue: [
          { required: true, message: "触发值不能为空", trigger: "blur" }
        ]
      },
      // 信息表单校验
      infoRules: {
        hint: [
          { required: true, message: "提示信息不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDeviceOptions();
    this.getPointOptions();
  },
  methods: {
    // 获取设备选项
    getDeviceOptions() {
      deviceOption({ clientAccess: 'R/W' }).then(response => {
        this.deviceOptions = response.data || [];
      });
    },
    // 获取点位选项
    getPointOptions() {
      devicePointOption().then(response => {
        this.pointOptions = response.data || [];
      });
    },
    // 查询条件中设备变化时，获取对应的点位选项
    handleQueryDeviceChange(deviceId) {
      this.queryParams.pointId = undefined;
      this.queryPointOptions = [];
      if (deviceId) {
        devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
          this.queryPointOptions = response.data || [];
        }).catch(error => {
          console.error('获取查询设备点位失败:', error);
        });
      }
    },
    // 查询条件中被锁设备变化时，获取对应的点位选项
    handleQueryLockDeviceChange(deviceId) {
      this.queryParams.lockPointId = undefined;
      this.queryLockPointOptions = [];
      if (deviceId) {
        devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
          this.queryLockPointOptions = response.data || [];
        }).catch(error => {
          console.error('获取查询被锁设备点位失败:', error);
        });
      }
    },
    // 触发设备变化时，获取对应的点位选项
    handleTriggerDeviceChange(deviceId) {
      this.form.pointId = undefined;
      this.triggerPointOptions = [];
      if (deviceId) {
        // 传入deviceId参数查询特定设备的点位，并过滤可读写的点位
        devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
          this.triggerPointOptions = response.data || [];
        }).catch(error => {
          console.error('获取触发设备点位失败:', error);
          this.$modal.msgError('获取设备点位失败');
        });
      }
    },
    // 被锁设备变化时，获取对应的点位选项
    handleLockDeviceChange(deviceId) {
      this.form.lockPointId = undefined;
      this.lockPointOptions = [];
      if (deviceId) {
        // 传入deviceId参数查询特定设备的点位，并过滤可读写的点位
        devicePointOption({ deviceId: deviceId, clientAccess: 'R/W' }).then(response => {
          this.lockPointOptions = response.data || [];
        }).catch(error => {
          console.error('获取被锁设备点位失败:', error);
          this.$modal.msgError('获取设备点位失败');
        });
      }
    },
    // 获取符号类型样式
    getSymbolType(symbol) {
      const typeMap = {
        '==': 'success',
        '!=': 'warning',
        '>': 'primary',
        '<': 'primary',
        '>=': 'info',
        '<=': 'info'
      };
      return typeMap[symbol] || 'default';
    },

    /** 查询点位锁列表 */
    getList() {
      this.loading = true;
      listPointLock(this.queryParams).then(response => {
        this.pointLockList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deviceId: undefined,
        pointId: undefined,
        lockDeviceId: undefined,
        lockPointId: undefined,
        lockValue: undefined,
        symbol: undefined,
        hint: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.triggerPointOptions = [];
      this.lockPointOptions = [];
      this.open = true;
      this.title = "添加点位互锁";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getPointLock(id).then(response => {
        this.loading = false;
        this.form = response.data;

        // 根据已选择的设备加载对应的点位选项
        const loadPromises = [];

        if (this.form.deviceId) {
          const triggerPromise = devicePointOption({ deviceId: this.form.deviceId, clientAccess: 'R/W' }).then(response => {
            this.triggerPointOptions = response.data || [];
          }).catch(error => {
            console.error('获取触发设备点位失败:', error);
          });
          loadPromises.push(triggerPromise);
        }

        if (this.form.lockDeviceId) {
          const lockPromise = devicePointOption({ deviceId: this.form.lockDeviceId, clientAccess: 'R/W' }).then(response => {
            this.lockPointOptions = response.data || [];
          }).catch(error => {
            console.error('获取被锁设备点位失败:', error);
          });
          loadPromises.push(lockPromise);
        }

        // 等待所有点位数据加载完成后再打开对话框
        Promise.all(loadPromises).finally(() => {
          this.open = true;
          this.title = "修改点位互锁";
        });
      }).catch(error => {
        this.loading = false;
        console.error('获取点位锁详情失败:', error);
        this.$modal.msgError('获取数据失败');
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 验证所有表单
      Promise.all([
        new Promise((resolve, reject) => {
          this.$refs["triggerForm"].validate(valid => {
            if (valid) resolve();
            else reject('触发设备选择有误');
          });
        }),
        new Promise((resolve, reject) => {
          this.$refs["lockForm"].validate(valid => {
            if (valid) resolve();
            else reject('被锁设备选择有误');
          });
        }),
        new Promise((resolve, reject) => {
          this.$refs["conditionForm"].validate(valid => {
            if (valid) resolve();
            else reject('触发条件设置有误');
          });
        }),
        new Promise((resolve, reject) => {
          this.$refs["infoForm"].validate(valid => {
            if (valid) resolve();
            else reject('提示信息设置有误');
          });
        })
      ]).then(() => {
        this.buttonLoading = true;
        if (this.form.id != null) {
          updatePointLock(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          }).finally(() => {
            this.buttonLoading = false;
          });
        } else {
          addPointLock(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }).finally(() => {
            this.buttonLoading = false;
          });
        }
      }).catch(error => {
        this.$modal.msgError(error || '表单验证失败');
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除点位锁编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delPointLock(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/pointLock/export', {
        ...this.queryParams
      }, `pointLock_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

/* 设备选择区域 */
.device-selection-row {
  margin-bottom: 20px;
}

.selection-card {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background: #F8F9FA;
}

.selection-card .el-card__body {
  padding: 20px;
}

/* 触发条件区域 */
.condition-row {
  margin-bottom: 20px;
}

.condition-card {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background: #F8F9FA;
}

.condition-card .el-card__body {
  padding: 20px;
}

/* 信息区域 */
.info-row {
  margin-bottom: 20px;
}

.info-card {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background: #F8F9FA;
}

.info-card .el-card__body {
  padding: 20px;
}

/* 箭头指示 */
.arrow-col {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
}

.arrow-icon {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 8px;
}

.arrow-text {
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.dialog-footer {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #EBEEF5;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .arrow-col {
    display: none;
  }

  .section-title {
    text-align: center;
    margin: 20px 0;
  }

  .device-selection-row .el-col,
  .condition-row .el-col,
  .info-row .el-col {
    margin-bottom: 15px;
  }
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 18px;
}
</style>

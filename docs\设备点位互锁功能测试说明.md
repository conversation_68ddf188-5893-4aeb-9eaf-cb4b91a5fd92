# 设备点位互锁功能测试说明

## 测试环境准备

### 前置条件
1. 系统中已存在设备数据
2. 设备下已配置点位数据
3. 用户具有点位互锁管理权限

### 测试数据要求
- 至少2个不同的设备
- 每个设备至少有2个点位
- 点位数据类型支持数值比较

## 功能测试用例

### 1. 界面布局测试

#### 测试步骤
1. 进入设备点位互锁管理页面
2. 点击"新增"按钮

#### 预期结果
- 弹出1000px宽度的对话框
- 界面分为三个清晰的层次：
  - 第一层：设备和点位选择（左右布局）
  - 第二层：触发条件设置（横向布局）
  - 第三层：提示信息和备注（左右布局）
- 每个区域有不同的背景色和图标标识

### 2. 设备点位联动测试

#### 测试步骤
1. 在"触发设备选择"区域选择一个设备
2. 观察"触发点位"下拉框变化
3. 在"被锁设备选择"区域选择另一个设备
4. 观察"被锁点位"下拉框变化

#### 预期结果
- 选择设备后，对应的点位下拉框自动加载该设备的点位选项
- 未选择设备时，点位下拉框处于禁用状态
- 切换设备时，已选择的点位会被清空

### 3. 触发条件设置测试

#### 测试步骤
1. 完成设备和点位选择
2. 在触发条件区域选择条件符号（如"等于"）
3. 输入触发值（如"1"）

#### 预期结果
- 触发条件和触发值正确保存
- 界面布局清晰，左右两列平均分布

### 4. 表单验证测试

#### 测试步骤
1. 尝试提交空表单
2. 逐步填写必填字段，观察验证提示
3. 提交完整表单

#### 预期结果
- 空表单提交时显示相应的验证错误
- 验证错误按区域分类显示：
  - "触发设备选择有误"
  - "被锁设备选择有误"
  - "触发条件设置有误"
  - "提示信息设置有误"
- 所有必填字段填写完整后可以成功提交

### 5. 查询功能测试

#### 测试步骤
1. 在查询条件中选择设备
2. 观察对应点位下拉框的变化
3. 执行查询操作

#### 预期结果
- 选择查询设备后，点位选项自动过滤为该设备的点位
- 查询结果正确显示匹配的互锁配置
- 表格显示设备名称和点位名称而非ID

### 6. 编辑功能测试

#### 测试步骤
1. 点击列表中某条记录的"修改"按钮
2. 观察对话框中数据的加载情况
3. 修改部分配置并保存

#### 预期结果
- 对话框正确显示现有配置数据
- 根据已选设备自动加载对应的点位选项
- 修改后的数据正确保存

## API接口测试

### 1. 设备选项接口测试
```javascript
// 测试获取设备选项
deviceOption().then(response => {
  console.log('设备选项:', response.data);
});
```

### 2. 设备点位选项接口测试
```javascript
// 测试获取所有点位选项
devicePointOption().then(response => {
  console.log('所有点位选项:', response.data);
});

// 测试根据设备ID获取点位选项
devicePointOption({ deviceId: 'DEVICE_ID' }).then(response => {
  console.log('特定设备点位选项:', response.data);
});
```

## 性能测试

### 1. 大数据量测试
- 测试100+设备的选择性能
- 测试1000+点位的加载性能
- 验证搜索过滤功能的响应速度

### 2. 并发操作测试
- 多用户同时配置互锁规则
- 验证数据一致性

## 兼容性测试

### 1. 浏览器兼容性
- Chrome（推荐）
- Firefox
- Safari
- Edge

### 2. 响应式测试
- 桌面端（1920x1080）
- 平板端（768px宽度）
- 移动端（375px宽度）

## 常见问题排查

### 1. 点位选项不加载
- 检查devicePointOption接口是否正常
- 确认传入的deviceId参数格式正确
- 查看浏览器控制台错误信息

### 2. 界面样式异常
- 检查CSS样式是否正确加载
- 确认所有区域都使用统一的灰色背景
- 验证响应式布局在不同屏幕尺寸下的表现

### 3. 表单验证失败
- 确认所有必填字段都已填写
- 检查表单ref引用是否正确
- 验证验证规则配置

## 测试报告模板

### 测试结果记录
- [ ] 界面布局正常
- [ ] 设备点位联动正常
- [ ] 触发条件设置正常
- [ ] 表单验证正常
- [ ] 查询功能正常
- [ ] 编辑功能正常
- [ ] API接口正常
- [ ] 性能表现良好
- [ ] 兼容性良好

### 发现的问题
1. 问题描述：
   - 重现步骤：
   - 预期结果：
   - 实际结果：
   - 严重程度：

### 改进建议
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议
